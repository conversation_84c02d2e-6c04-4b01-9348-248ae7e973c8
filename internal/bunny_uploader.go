package internal

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
)

// BunnyUploader handles uploading HLS segments to BunnyCDN
type BunnyUploader struct {
	cfg           *Config
	streamID      string
	hlsOutDir     string
	uploadedFiles map[string]bool
	uploadMutex   sync.Mutex
	watcher       *fsnotify.Watcher
	stopChan      chan struct{}
	httpClient    *http.Client
}

// NewBunnyUploader creates a new BunnyCDN uploader instance
func NewBunnyUploader(cfg *Config, hlsOutDir, streamID string) (*BunnyUploader, error) {
	if cfg.BunnyUsername == "" || cfg.BunnyPassword == "" || cfg.BunnyEndpoint == "" {
		return nil, fmt.Errorf("BunnyCDN credentials not set")
	}

	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return nil, fmt.Errorf("failed to create file watcher: %v", err)
	}

	uploader := &BunnyUploader{
		cfg:           cfg,
		streamID:      streamID,
		hlsOutDir:     hlsOutDir,
		uploadedFiles: make(map[string]bool),
		watcher:       watcher,
		stop<PERSON>han:      make(chan struct{}),
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}

	return uploader, nil
}

// Start begins monitoring and uploading HLS segments
func (bu *BunnyUploader) Start() error {
	log.Printf("Starting BunnyCDN uploader for stream: %s", bu.streamID)

	// Add the HLS output directory to watcher
	if err := bu.watcher.Add(bu.hlsOutDir); err != nil {
		return fmt.Errorf("failed to add directory to watcher: %v", err)
	}

	// Upload existing files first (in case of restart)
	go func() {
		if err := bu.uploadExistingFiles(); err != nil {
			log.Printf("Failed to upload existing files for stream %s: %v", bu.streamID, err)
		}
	}()

	// Start file monitoring
	go bu.monitorFiles()

	return nil
}

// Stop stops the uploader and cleans up resources
func (bu *BunnyUploader) Stop() {
	log.Printf("Stopping BunnyCDN uploader for stream: %s", bu.streamID)
	close(bu.stopChan)
	if bu.watcher != nil {
		bu.watcher.Close()
	}
}

// monitorFiles watches for new files and uploads them
func (bu *BunnyUploader) monitorFiles() {
	for {
		select {
		case <-bu.stopChan:
			return

		case event, ok := <-bu.watcher.Events:
			if !ok {
				log.Printf("File watcher closed for stream: %s", bu.streamID)
				return
			}

			// Handle file creation and write events
			if event.Op&fsnotify.Create == fsnotify.Create || event.Op&fsnotify.Write == fsnotify.Write {
				fileName := filepath.Base(event.Name)

				// Only process .ts and .m3u8 files
				if bu.isHLSFile(fileName) {
					bu.uploadMutex.Lock()
					_, alreadyUploaded := bu.uploadedFiles[fileName]
					bu.uploadMutex.Unlock()

					// Always re-upload .m3u8 files (they change frequently in live streams)
					// Only upload .ts files once (they are immutable)
					if !alreadyUploaded || strings.HasSuffix(fileName, ".m3u8") {
						go bu.handleFileUpload(event.Name, fileName)
					}
				}
			}

			// Handle file removal (cleanup tracking)
			if event.Op&fsnotify.Remove == fsnotify.Remove {
				fileName := filepath.Base(event.Name)
				bu.uploadMutex.Lock()
				delete(bu.uploadedFiles, fileName)
				bu.uploadMutex.Unlock()
			}

		case err, ok := <-bu.watcher.Errors:
			if !ok {
				log.Printf("File watcher error channel closed for stream: %s", bu.streamID)
				return
			}
			log.Printf("File watcher error for stream %s: %v", bu.streamID, err)
		}
	}
}

// handleFileUpload processes a single file upload
func (bu *BunnyUploader) handleFileUpload(filePath, fileName string) {
	// Wait a moment to ensure file is fully written
	time.Sleep(500 * time.Millisecond)

	if err := bu.uploadFile(filePath, fileName); err != nil {
		log.Printf("Failed to upload %s for stream %s: %v", fileName, bu.streamID, err)
	} else {
		bu.uploadMutex.Lock()
		bu.uploadedFiles[fileName] = true
		bu.uploadMutex.Unlock()
		log.Printf("Successfully uploaded %s for stream %s", fileName, bu.streamID)
	}
}

// uploadExistingFiles uploads any existing files in the directory
func (bu *BunnyUploader) uploadExistingFiles() error {
	files, err := os.ReadDir(bu.hlsOutDir)
	if err != nil {
		return fmt.Errorf("failed to read HLS directory: %v", err)
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		fileName := file.Name()
		if bu.isHLSFile(fileName) {
			filePath := filepath.Join(bu.hlsOutDir, fileName)

			if err := bu.uploadFile(filePath, fileName); err != nil {
				log.Printf("Failed to upload existing file %s for stream %s: %v", fileName, bu.streamID, err)
			} else {
				bu.uploadMutex.Lock()
				bu.uploadedFiles[fileName] = true
				bu.uploadMutex.Unlock()
				log.Printf("Successfully uploaded existing file %s for stream %s", fileName, bu.streamID)
			}
		}
	}

	return nil
}

// uploadFile uploads a single file to BunnyCDN Storage
func (bu *BunnyUploader) uploadFile(localFilePath, fileName string) error {
	// Read the file
	fileData, err := os.ReadFile(localFilePath)
	if err != nil {
		return fmt.Errorf("failed to read file %s: %v", localFilePath, err)
	}

	// Construct the remote path
	remotePath := bu.buildRemotePath(fileName)
	uploadURL := fmt.Sprintf("https://%s/%s/%s", bu.cfg.BunnyEndpoint, bu.cfg.BunnyUsername, remotePath)

	// Create the HTTP request
	req, err := http.NewRequest("PUT", uploadURL, bytes.NewReader(fileData))
	if err != nil {
		return fmt.Errorf("failed to create upload request: %v", err)
	}

	// Set headers
	req.Header.Set("AccessKey", bu.cfg.BunnyPassword)
	req.Header.Set("Content-Type", bu.getContentType(fileName))

	// Set cache control headers - no cache for .m3u8 files, long cache for .ts files
	if strings.HasSuffix(fileName, ".m3u8") {
		req.Header.Set("Cache-Control", "no-cache, no-store, must-revalidate")
		req.Header.Set("Pragma", "no-cache")
		req.Header.Set("Expires", "0")
	} else if strings.HasSuffix(fileName, ".ts") {
		req.Header.Set("Cache-Control", "public, max-age=31536000") // 1 year cache for segments
	}

	req.ContentLength = int64(len(fileData))

	// Execute the request
	resp, err := bu.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to upload file: %v", err)
	}
	defer resp.Body.Close()

	// Check response
	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Purge CDN cache for .m3u8 files immediately (critical for live streaming)
	if strings.HasSuffix(fileName, ".m3u8") {
		go func() {
			// Purge immediately
			if err := bu.purgeCDNCache(remotePath); err != nil {
				log.Printf("Failed to purge CDN cache for %s: %v", remotePath, err)
			} else {
				log.Printf("Successfully purged CDN cache for %s", fileName)
			}

			// Purge again after 1 second to ensure cache is cleared
			time.Sleep(1 * time.Second)
			if err := bu.purgeCDNCache(remotePath); err != nil {
				log.Printf("Failed to purge CDN cache (retry) for %s: %v", remotePath, err)
			} else {
				log.Printf("Successfully purged CDN cache (retry) for %s", fileName)
			}
		}()
	}

	return nil
}

// buildRemotePath constructs the remote path for a file
func (bu *BunnyUploader) buildRemotePath(fileName string) string {
	remotePath := strings.TrimPrefix(bu.cfg.BunnyStoragePath, "/")
	if remotePath != "" && !strings.HasSuffix(remotePath, "/") {
		remotePath += "/"
	}
	return remotePath + bu.streamID + "/" + fileName
}

// isHLSFile checks if a file is an HLS file that should be uploaded
func (bu *BunnyUploader) isHLSFile(fileName string) bool {
	return strings.HasSuffix(fileName, ".ts") || strings.HasSuffix(fileName, ".m3u8")
}

// getContentType returns the appropriate content type for the file
func (bu *BunnyUploader) getContentType(fileName string) string {
	switch {
	case strings.HasSuffix(fileName, ".ts"):
		return "video/mp2t"
	case strings.HasSuffix(fileName, ".m3u8"):
		return "application/vnd.apple.mpegurl"
	default:
		return "application/octet-stream"
	}
}

// purgeCDNCache purges the CDN cache for a specific file
func (bu *BunnyUploader) purgeCDNCache(remotePath string) error {
	if bu.cfg.BunnyAPIKey == "" {
		return fmt.Errorf("BUNNY_API_KEY not set")
	}

	// Construct the purge URL
	fileURL := fmt.Sprintf("https://%s/%s", bu.cfg.BunnyHOST, remotePath)
	purgeURL := fmt.Sprintf("https://api.bunny.net/purge?url=%s", fileURL)

	// Create the request
	req, err := http.NewRequest("POST", purgeURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create purge request: %v", err)
	}

	req.Header.Set("AccessKey", bu.cfg.BunnyAPIKey)

	// Execute the request
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to purge cache: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("purge failed with status %d: %s", resp.StatusCode, string(body))
	}

	return nil
}

// CleanBunnyCDNDir deletes the entire folder for a stream key from BunnyCDN Storage API
func CleanBunnyCDNDir(cfg *Config, relPath string) error {
	if cfg.BunnyUsername == "" || cfg.BunnyPassword == "" || cfg.BunnyEndpoint == "" {
		return fmt.Errorf("BunnyCDN credentials not set")
	}

	folderURL := fmt.Sprintf("https://%s/%s/%s/", cfg.BunnyEndpoint, cfg.BunnyUsername, strings.TrimPrefix(relPath, "/"))
	log.Printf("Deleting BunnyCDN folder: %s", folderURL)

	// DELETE request to remove folder (Storage API)
	req, err := http.NewRequest("DELETE", folderURL, nil)
	if err != nil {
		return err
	}
	req.Header.Set("AccessKey", cfg.BunnyPassword)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to delete BunnyCDN folder: %d %s - %s", resp.StatusCode, resp.Status, string(body))
	}

	// Purge CDN cache for the folder using POST and Bunny API Key
	if cfg.BunnyAPIKey == "" {
		return fmt.Errorf("BUNNY_API_KEY not set in config")
	}

	folderURL = fmt.Sprintf("https://%s/%s/*", cfg.BunnyHOST, strings.TrimPrefix(relPath, "/"))
	purgeURL := fmt.Sprintf("https://api.bunny.net/purge?url=%s", folderURL)
	log.Printf("Purging BunnyCDN folder: %s", purgeURL)

	req, err = http.NewRequest("POST", purgeURL, nil)
	if err != nil {
		return err
	}
	req.Header.Set("AccessKey", cfg.BunnyAPIKey)

	resp, err = http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to purge BunnyCDN folder: %d %s - %s", resp.StatusCode, resp.Status, string(body))
	} else {
		log.Printf("Purged BunnyCDN folder: %s", folderURL)
	}

	return nil
}
