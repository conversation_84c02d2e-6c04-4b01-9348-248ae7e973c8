package internal

import (
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/nareix/joy4/format/flv"
	"github.com/nareix/joy4/format/rtmp"
)

// StreamManager manages active streams and their uploaders
type StreamManager struct {
	uploaders map[string]*BunnyUploader
	mutex     sync.RWMutex
}

// NewStreamManager creates a new stream manager
func NewStreamManager() *StreamManager {
	return &StreamManager{
		uploaders: make(map[string]*BunnyUploader),
	}
}

// AddUploader adds a new uploader for a stream
func (sm *StreamManager) AddUploader(streamID string, uploader *BunnyUploader) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	sm.uploaders[streamID] = uploader
}

// RemoveUploader removes and stops an uploader for a stream
func (sm *StreamManager) RemoveUploader(streamID string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	if uploader, exists := sm.uploaders[streamID]; exists {
		uploader.Stop()
		delete(sm.uploaders, streamID)
	}
}

// Global stream manager
var streamManager = NewStreamManager()

// StartRTMPServer starts the RTMP server to ingest live streams
func StartRTMPServer(cfg *Config) {
	server := &rtmp.Server{
		Addr: ":" + cfg.RTMPPort,
		HandlePublish: func(conn *rtmp.Conn) {
			pathArr := strings.Split(conn.URL.Path, "/")
			streamKey := pathArr[len(pathArr)-1]
			streamID := pathArr[len(pathArr)-2]

			if cfg.StreamKey != streamKey {
				log.Println("Invalid streamKey")
				conn.Close()
				return
			}

			log.Printf("Incoming RTMP stream id: %s", streamID)

			// Clean local and BunnyCDN HLS directories for this stream
			if err := CleanLocalHLSDir(cfg, streamID); err != nil {
				log.Printf("Failed to clean local HLS dir: %v", err)
			} else {
				log.Printf("Local HLS dir cleaned for %s", streamID)
			}

			if !cfg.LocalMode {
				cleanPath := filepath.Join(strings.TrimPrefix(cfg.BunnyStoragePath, "/"), streamID)
				if err := CleanBunnyCDNDir(cfg, cleanPath); err != nil {
					log.Printf("Failed to clean BunnyCDN dir: %v", err)
				} else {
					log.Printf("BunnyCDN dir cleaned for %s", cleanPath)
				}
				// Wait a moment to allow BunnyCDN to sync deletions
				time.Sleep(1 * time.Second)
			}

			// Ensure HLS output directory exists
			hlsOutDir := filepath.Join(cfg.HLSDir, streamID)
			if err := os.MkdirAll(hlsOutDir, 0755); err != nil {
				log.Printf("Failed to create HLS output dir %s: %v", hlsOutDir, err)
				return
			}

			// Start FFmpeg process with stdin input
			var cmd *exec.Cmd
			if cfg.LocalMode {
				cmd = createLocalFFmpegStdinCommand(cfg, streamID, hlsOutDir)
			} else {
				cmd = createBunnyFFmpegStdinCommand(cfg, streamID, hlsOutDir)
			}

			// Get stdin pipe for FFmpeg
			stdin, err := cmd.StdinPipe()
			if err != nil {
				log.Printf("Failed to get FFmpeg stdin pipe for stream %s: %v", streamID, err)
				return
			}

			log.Printf("Starting FFmpeg for stream %s", streamID)

			// Start FFmpeg in a goroutine
			go func() {
				defer stdin.Close()
				if err := cmd.Run(); err != nil {
					log.Printf("FFmpeg error for stream %s: %v", streamID, err)
				}
				log.Printf("FFmpeg finished for stream %s", streamID)

				// Clean up uploader when FFmpeg stops
				if !cfg.LocalMode {
					streamManager.RemoveUploader(streamID)
				}
			}()

			// Start BunnyCDN uploader if not in local mode
			if !cfg.LocalMode {
				uploader, err := NewBunnyUploader(cfg, hlsOutDir, streamID)
				if err != nil {
					log.Printf("Failed to create BunnyCDN uploader for stream %s: %v", streamID, err)
				} else {
					if err := uploader.Start(); err != nil {
						log.Printf("Failed to start BunnyCDN uploader for stream %s: %v", streamID, err)
					} else {
						streamManager.AddUploader(streamID, uploader)
					}
				}
			}

			// Handle the publishing stream - use joy4 to copy stream to FLV format
			log.Printf("Starting stream copy from RTMP to FLV for stream %s", streamID)

			// Create FLV muxer that writes to FFmpeg stdin
			flvMuxer := flv.NewMuxer(stdin)

			// Copy stream headers first
			streams, err := conn.Streams()
			if err != nil {
				log.Printf("Failed to get stream info for %s: %v", streamID, err)
				return
			}

			if err := flvMuxer.WriteHeader(streams); err != nil {
				log.Printf("Failed to write FLV header for stream %s: %v", streamID, err)
				return
			}

			// Copy packets from RTMP to FLV
			packetCount := 0
			for {
				packet, err := conn.ReadPacket()
				if err != nil {
					log.Printf("RTMP connection error for stream %s: %v", streamID, err)
					break
				}

				packetCount++
				if packetCount%100 == 0 {
					log.Printf("Processed %d packets for stream %s", packetCount, streamID)
				}

				// Write packet to FLV muxer
				if err := flvMuxer.WritePacket(packet); err != nil {
					log.Printf("Failed to write packet to FLV for stream %s: %v", streamID, err)
					break
				}
			}

			log.Printf("RTMP connection closed for stream %s", streamID)

			// Kill FFmpeg process
			if cmd.Process != nil {
				cmd.Process.Kill()
			}

			// Clean up uploader
			if !cfg.LocalMode {
				streamManager.RemoveUploader(streamID)
			}
		},
	}

	log.Printf("RTMP server listening on :%s", cfg.RTMPPort)
	if err := server.ListenAndServe(); err != nil {
		log.Fatalf("RTMP server error: %v", err)
	}
}

// createLocalFFmpegStdinCommand creates FFmpeg command for local mode using stdin
func createLocalFFmpegStdinCommand(cfg *Config, streamID, hlsOutDir string) *exec.Cmd {
	args := []string{
		"-v", "verbose", // Add verbose logging
		"-f", "flv",
		"-i", "pipe:0", // Read from stdin
		"-c:v", "libx264",
		"-preset", "veryfast",
		"-tune", "zerolatency",
		"-crf", "23",
		"-maxrate", "3000k",
		"-bufsize", "6000k",
		"-c:a", "aac",
		"-b:a", "128k",
		"-ar", "44100",
		"-ac", "2",
		"-f", "hls",
		"-hls_time", "2",
		"-hls_list_size", "10",
		"-hls_flags", "delete_segments+append_list",
		"-hls_segment_filename", filepath.Join(hlsOutDir, "segment_%03d.ts"),
		"-hls_playlist_type", "event",
		"-hls_allow_cache", "0",
		"-hls_segment_type", "mpegts",
		"-master_pl_name", "master.m3u8",
		filepath.Join(hlsOutDir, "index.m3u8"),
	}

	cmd := exec.Command("ffmpeg", args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd
}

// createBunnyFFmpegStdinCommand creates FFmpeg command for BunnyCDN mode using stdin
func createBunnyFFmpegStdinCommand(cfg *Config, streamID, hlsOutDir string) *exec.Cmd {
	args := []string{
		"-v", "verbose", // Add verbose logging
		"-f", "flv",
		"-i", "pipe:0", // Read from stdin
		"-c:v", "libx264",
		"-preset", "fast",
		"-tune", "zerolatency",
		"-crf", "23",
		"-maxrate", "3000k",
		"-bufsize", "6000k",
		"-keyint_min", "60",
		"-g", "60",
		"-sc_threshold", "0",
		"-c:a", "aac",
		"-b:a", "128k",
		"-ar", "44100",
		"-ac", "2",
		"-f", "hls",
		"-hls_time", "4",
		"-hls_list_size", "10",
		"-hls_flags", "delete_segments+append_list",
		"-hls_segment_filename", filepath.Join(hlsOutDir, "segment_%03d.ts"),
		"-hls_playlist_type", "event",
		"-hls_allow_cache", "0",
		"-hls_segment_type", "mpegts",
		"-master_pl_name", "master.m3u8",
		filepath.Join(hlsOutDir, "index.m3u8"),
	}

	cmd := exec.Command("ffmpeg", args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd
}

// CleanLocalHLSDir deletes the local HLS directory for a stream key
func CleanLocalHLSDir(cfg *Config, streamKey string) error {
	dir := filepath.Join(cfg.HLSDir, streamKey)
	if _, err := os.Stat(dir); err == nil {
		log.Printf("Deleting local HLS dir: %s", dir)
		return os.RemoveAll(dir)
	}
	return nil
}
