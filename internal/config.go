package internal

import (
	"log"
	"os"
)

type Config struct {
	RTMPPort         string
	HTTPPort         string
	HLSDir           string
	BunnyEndpoint    string
	BunnyUsername    string
	BunnyPassword    string
	BunnyStoragePath string
	BunnyHOST        string
	BunnyBaseURL     string
	BunnyAPIKey      string
	LocalMode        bool
	StreamMode       string
	StreamKey        string
}

func LoadConfig() *Config {
	cfg := &Config{
		RTMPPort:         getEnv("RTMP_PORT", "1935"),
		HTTPPort:         getEnv("HTTP_PORT", "8080"),
		HLSDir:           getEnv("HLS_DIR", "./hls"),
		BunnyEndpoint:    getEnv("BUNNY_CDN_ENDPOINT", ""),
		BunnyUsername:    getEnv("BUNNY_CDN_USERNAME", ""),
		BunnyPassword:    getEnv("BUNNY_CDN_PASSWORD", ""),
		BunnyStoragePath: getEnv("BUNNY_CDN_STORAGE_PATH", ""),
		BunnyHOST:        getEnv("BUNNY_CDN_HOST", ""),
		BunnyBaseURL:     getEnv("BUNNY_CDN_URL", ""),
		BunnyAPIKey:      getEnv("BUNNY_API_KEY", ""),
		LocalMode:        getEnv("LOCAL_MODE", "false") == "true",
		StreamMode:       getEnv("STREAM_MODE", "hls"),
		StreamKey:        getEnv("STREAM_KEY", ""),
	}
	if cfg.BunnyEndpoint == "" || cfg.BunnyUsername == "" || cfg.BunnyPassword == "" || cfg.BunnyStoragePath == "" || cfg.BunnyBaseURL == "" {
		log.Fatal("Missing Bunny CDN configuration in environment variables.")
	}
	return cfg
}

func getEnv(key, fallback string) string {
	if value, ok := os.LookupEnv(key); ok {
		return value
	}
	return fallback
}
