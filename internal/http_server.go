package internal

import (
	"fmt"
	"html/template"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// StartHTTPServer starts an HTTP server to serve playlist URLs and a dynamic player
func StartHTTPServer(cfg *Config) {
	http.HandleFunc("/hls/", func(w http.ResponseWriter, r *http.Request) {
		// Add CORS headers for HLS playback
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

		// Handle preflight requests
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		localPath := filepath.Join(cfg.HLSDir, r.URL.Path[len("/hls/"):])
		if _, err := os.Stat(localPath); err == nil {
			// Set appropriate content type and cache control for HLS files
			if strings.HasSuffix(localPath, ".m3u8") {
				w.<PERSON><PERSON>().Set("Content-Type", "application/vnd.apple.mpegurl")
				// Prevent caching of playlist files
				w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate, max-age=0")
				w.Header().Set("Pragma", "no-cache")
				w.Header().Set("Expires", "0")
				w.Header().Set("Last-Modified", time.Now().UTC().Format(http.TimeFormat))
				w.Header().Set("ETag", fmt.Sprintf(`"%d"`, time.Now().UnixNano()))
			} else if strings.HasSuffix(localPath, ".ts") {
				w.Header().Set("Content-Type", "video/mp2t")
				// Allow long-term caching of segment files
				w.Header().Set("Cache-Control", "public, max-age=31536000")
			}
			http.ServeFile(w, r, localPath)
			return
		}
		// Fallback: try with /live/ prefix if not already present
		parts := strings.SplitN(r.URL.Path[len("/hls/"):], "/", 2)
		if len(parts) == 2 && parts[0] != "live" {
			altPath := filepath.Join(cfg.HLSDir, "live", parts[0], parts[1])
			if _, err := os.Stat(altPath); err == nil {
				// Set appropriate content type and cache control for HLS files
				if strings.HasSuffix(altPath, ".m3u8") {
					w.Header().Set("Content-Type", "application/vnd.apple.mpegurl")
					// Prevent caching of playlist files
					w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate, max-age=0")
					w.Header().Set("Pragma", "no-cache")
					w.Header().Set("Expires", "0")
					w.Header().Set("Last-Modified", time.Now().UTC().Format(http.TimeFormat))
					w.Header().Set("ETag", fmt.Sprintf(`"%d"`, time.Now().UnixNano()))
				} else if strings.HasSuffix(altPath, ".ts") {
					w.Header().Set("Content-Type", "video/mp2t")
					// Allow long-term caching of segment files
					w.Header().Set("Cache-Control", "public, max-age=31536000")
				}
				http.ServeFile(w, r, altPath)
				return
			}
		}
		http.NotFound(w, r)
	})

	// Playlist Redirector: /playlist/{streamKey}
	http.HandleFunc("/playlist/", func(w http.ResponseWriter, r *http.Request) {
		streamKey := strings.TrimPrefix(r.URL.Path, "/playlist/")
		if streamKey == "" {
			http.Error(w, "Missing stream key", http.StatusBadRequest)
			return
		}
		var playlistURL string
		if cfg.LocalMode {
			playlistURL = fmt.Sprintf("/hls/%s/index.m3u8", streamKey)
		} else {
			// Add timestamp and random component to prevent browser caching of playlist
			playlistURL = fmt.Sprintf("%s/%s/index.m3u8?t=%d&r=%d", cfg.BunnyBaseURL, streamKey, time.Now().Unix(), time.Now().UnixNano())
		}
		log.Printf("Redirecting to playlist: %s", playlistURL)
		http.Redirect(w, r, playlistURL, http.StatusFound)
	})

	// Dynamic Player: /player/{streamKey}
	http.HandleFunc("/player/", func(w http.ResponseWriter, r *http.Request) {
		streamKey := strings.TrimPrefix(r.URL.Path, "/player/")
		if streamKey == "" {
			http.Error(w, "Missing stream key", http.StatusBadRequest)
			return
		}
		playlistURL := fmt.Sprintf("/hls/%s/index.m3u8", streamKey)
		data := struct {
			PlaylistURL string
			StreamKey   string
		}{
			PlaylistURL: playlistURL,
			StreamKey:   streamKey,
		}

		tmpl, err := template.ParseFiles("player.html")
		if err != nil {
			http.Error(w, "Could not load player template", http.StatusInternalServerError)
			log.Printf("Error parsing player template: %v", err)
			return
		}

		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		if err := tmpl.Execute(w, data); err != nil {
			http.Error(w, "Could not render player", http.StatusInternalServerError)
			log.Printf("Error executing player template: %v", err)
		}
	})

	addr := ":" + cfg.HTTPPort
	log.Printf("HTTP server listening on %s", addr)
	if err := http.ListenAndServe(addr, nil); err != nil {
		log.Fatalf("HTTP server failed: %v", err)
	}
}
