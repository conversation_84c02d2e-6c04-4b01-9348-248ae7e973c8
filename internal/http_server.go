package internal

import (
	"fmt"
	"html/template"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

// StartHTTPServer starts an HTTP server to serve playlist URLs and a dynamic player
func StartHTTPServer(cfg *Config) {
	if cfg.LocalMode {
		// Serve HLS files directly from local disk, with fallback for /live/ prefix
		http.HandleFunc("/hls/", func(w http.ResponseWriter, r *http.Request) {
			// Add CORS headers for HLS playback
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

			// Handle preflight requests
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}

			localPath := filepath.Join(cfg.HLSDir, r.URL.Path[len("/hls/"):])
			if _, err := os.Stat(localPath); err == nil {
				// Set appropriate content type for HLS files
				if strings.HasSuffix(localPath, ".m3u8") {
					w.<PERSON>er().Set("Content-Type", "application/vnd.apple.mpegurl")
				} else if strings.HasSuffix(localPath, ".ts") {
					w.Header().Set("Content-Type", "video/mp2t")
				}
				http.ServeFile(w, r, localPath)
				return
			}
			// Fallback: try with /live/ prefix if not already present
			parts := strings.SplitN(r.URL.Path[len("/hls/"):], "/", 2)
			if len(parts) == 2 && parts[0] != "live" {
				altPath := filepath.Join(cfg.HLSDir, "live", parts[0], parts[1])
				if _, err := os.Stat(altPath); err == nil {
					// Set appropriate content type for HLS files
					if strings.HasSuffix(altPath, ".m3u8") {
						w.Header().Set("Content-Type", "application/vnd.apple.mpegurl")
					} else if strings.HasSuffix(altPath, ".ts") {
						w.Header().Set("Content-Type", "video/mp2t")
					}
					http.ServeFile(w, r, altPath)
					return
				}
			}
			http.NotFound(w, r)
		})
		log.Printf("[LOCAL MODE] Serving HLS files from %s (with /live/ fallback)", cfg.HLSDir)
	}

	// Playlist Redirector: /playlist/{streamKey}
	http.HandleFunc("/playlist/", func(w http.ResponseWriter, r *http.Request) {
		streamKey := strings.TrimPrefix(r.URL.Path, "/playlist/")
		if streamKey == "" {
			http.Error(w, "Missing stream key", http.StatusBadRequest)
			return
		}
		var playlistURL string
		if cfg.LocalMode {
			playlistURL = fmt.Sprintf("/hls/%s/index.m3u8", streamKey)
		} else {
			playlistURL = fmt.Sprintf("%s/live/%s/index.m3u8", cfg.BunnyBaseURL, streamKey)
		}
		log.Printf("Redirecting to playlist: %s", playlistURL)
		http.Redirect(w, r, playlistURL, http.StatusFound)
	})

	// Dynamic Player: /player/{streamKey}
	http.HandleFunc("/player/", func(w http.ResponseWriter, r *http.Request) {
		streamKey := strings.TrimPrefix(r.URL.Path, "/player/")
		if streamKey == "" {
			http.Error(w, "Missing stream key", http.StatusBadRequest)
			return
		}
		var playlistURL string
		if cfg.LocalMode {
			playlistURL = fmt.Sprintf("/hls/%s/index.m3u8", streamKey)
		} else {
			playlistURL = fmt.Sprintf("%s/live/%s/index.m3u8", cfg.BunnyBaseURL, streamKey)
		}
		data := struct {
			PlaylistURL string
			StreamKey   string
		}{
			PlaylistURL: playlistURL,
			StreamKey:   streamKey,
		}

		tmpl, err := template.ParseFiles("player.html")
		if err != nil {
			http.Error(w, "Could not load player template", http.StatusInternalServerError)
			log.Printf("Error parsing player template: %v", err)
			return
		}

		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		if err := tmpl.Execute(w, data); err != nil {
			http.Error(w, "Could not render player", http.StatusInternalServerError)
			log.Printf("Error executing player template: %v", err)
		}
	})

	addr := ":" + cfg.HTTPPort
	log.Printf("HTTP server listening on %s", addr)
	if err := http.ListenAndServe(addr, nil); err != nil {
		log.Fatalf("HTTP server failed: %v", err)
	}
}
