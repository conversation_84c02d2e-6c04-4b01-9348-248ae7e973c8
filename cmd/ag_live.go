package main

import (
	"ag_live/internal"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables from .env file
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, relying on environment variables.")
	}

	// Load configuration from environment variables
	cfg := internal.LoadConfig()

	// Start RTMP server (ingest)
	go internal.StartRTMPServer(cfg)

	// Start HTTP server for playlist serving
	go internal.StartHTTPServer(cfg)

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down...")
}
