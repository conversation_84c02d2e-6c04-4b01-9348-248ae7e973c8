# Go RTMP/HLS/BunnyCDN Backend

This application ingests RTMP streams (e.g., from OBS), segments them into HLS, uploads HLS files to Bunny CDN, and serves playlist URLs to clients.

## Features
- RTMP server (port 1935) using [joy5](https://github.com/nareix/joy5)
- FFmpeg-based HLS segmentation (500ms chunks)
- File watcher for HLS output
- Uploads .ts/.m3u8 to Bunny CDN Storage API with correct cache-control
- HTTP server (port 8080) to serve playlist URLs (redirects to Bunny CDN)
- Configurable via `.env` or environment variables

## Prerequisites
- Go 1.21+
- [FFmpeg](https://ffmpeg.org/) installed and in PATH

## Environment Variables (.env example)
```
# RTMP server port
RTMP_PORT=1935

# HTTP server port
HTTP_PORT=8080

# HLS output directory
HLS_DIR=./hls

# Bunny CDN SFTP endpoint
BUNNY_CDN_ENDPOINT=storage.bunnycdn.com

# Bunny CDN credentials
BUNNY_CDN_USERNAME=your-bunny-username
BUNNY_CDN_PASSWORD=your-bunny-password

# Storage path on Bunny CDN
BUNNY_CDN_STORAGE_PATH=/live-streams

# Base URL for accessing streams
BUNNY_CDN_URL=https://your-bunny-cdn-url/live-streams
```

## Usage
1. Build and run:
   ```sh
   go build -o ag_live .
   ./ag_live
   ```
2. In OBS, set your stream URL to `rtmp://<server-ip>:1935/<streamKey>`
3. Access playlist at `http://<server-ip>:8080/playlist/<streamKey>` (redirects to Bunny CDN)

## Notes
- Each stream key gets its own HLS folder and playlist.
- .ts files are cached long-term on CDN, .m3u8 playlists are not cached.
- See logs for errors and upload status.

## Testing Playback

You can test your stream playback using a simple HTML5 HLS player. Create a file called `player.html` with the following content:

```html
<!DOCTYPE html>
<html>
<head>
  <title>HLS Stream Player</title>
  <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
</head>
<body>
  <h1>HLS Stream Player</h1>
  <video id="video" controls width="640" height="360"></video>
  <script>
    var video = document.getElementById('video');
    var playlistUrl = 'https://agimgcdndev.b-cdn.net/live-streams/live/test-stream/playlist.m3u8'; // Change to your CDN URL
    if (Hls.isSupported()) {
      var hls = new Hls();
      hls.loadSource(playlistUrl);
      hls.attachMedia(video);
      hls.on(Hls.Events.MANIFEST_PARSED,function() {
        video.play();
      });
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      video.src = playlistUrl;
      video.addEventListener('loadedmetadata', function() {
        video.play();
      });
    }
  </script>
</body>
</html>
```

Open `player.html` in your browser and update the playlist URL as needed to match your BunnyCDN output. You should see your live stream if everything is working! 