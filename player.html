<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Live</title>
  <script src="https://cdn.jsdelivr.net/npm/hls.js@1.5.0"></script>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      background-color: black;
      height: 100%;
      width: 100%;
      overflow: hidden;
    }

    #video {
      width: 100%;
      object-fit: cover;
      background-color: black;
    }

    .live-indicator {
      position: absolute;
      top: 16px;
      left: 16px;
      background: red;
      color: white;
      padding: 4px 10px;
      font-size: 14px;
      font-weight: bold;
      border-radius: 4px;
      font-family: sans-serif;
      z-index: 10;
    }
  </style>
</head>
<body>

  <div class="live-indicator">LIVE</div>
  <video id="video" autoplay muted playsinline></video>

  <script>
    (function () {
      const video = document.getElementById('video');
      const playlistUrl = '{{.PlaylistURL}}'; // Injected from server

      if (!playlistUrl) {
        console.error('Missing playlist URL');
        return;
      }

      if (Hls.isSupported()) {
        const hls = new Hls({
        lowLatencyMode: true,
        liveSyncDuration: 1,
        maxBufferLength: 2,
        manifestLoadingTimeOut: 10000,
        levelLoadingTimeOut: 10000,
        fragLoadingTimeOut: 10000,
        maxLiveSyncPlaybackRate: 2,
        backBufferLength: 0,
        enableWorker: true,
        // Retry settings
        fragLoadingMaxRetry: 6,
        fragLoadingRetryDelay: 500,
        fragLoadingMaxRetryTimeout: 5000,
      });

        hls.loadSource(playlistUrl);
        hls.attachMedia(video);

        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          video.play().catch(err => console.warn('Autoplay error:', err));
        });

        hls.on(Hls.Events.ERROR, (event, data) => {
          console.error('HLS.js error:', data);
          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                hls.startLoad();
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                hls.recoverMediaError();
                break;
              default:
                hls.destroy();
                break;
            }
          }
        });

        // Auto-skip to live if behind by >1.5s
        setInterval(() => {
          if (hls.latency !== undefined && hls.latency > 1.5) {
            hls.goToLive();
          }
        }, 1000);
      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        video.src = playlistUrl;
        video.addEventListener('loadedmetadata', () => {
          video.play().catch(err => console.warn('Autoplay error:', err));
        });
      }
    })();
  </script>

</body>
</html>
